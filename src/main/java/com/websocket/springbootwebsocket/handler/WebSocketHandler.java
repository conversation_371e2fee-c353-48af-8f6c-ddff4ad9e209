package com.websocket.springbootwebsocket.handler;

import com.websocket.springbootwebsocket.entity.UserSession;
import com.websocket.springbootwebsocket.manager.UserSessionManager;
import com.websocket.springbootwebsocket.service.HeartbeatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

/**
 * WebSocket处理器 - 处理连接、断开和消息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketHandler implements org.springframework.web.socket.WebSocketHandler {

    private final UserSessionManager userSessionManager;
    private final HeartbeatService heartbeatService;
    
    /**
     * 连接建立后调用
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        String ipAddress = getClientIpAddress(session);

        // 创建用户会话并添加到管理器
        UserSession userSession = new UserSession(sessionId, ipAddress, session);
        userSessionManager.addSession(userSession);

        // 向客户端发送连接成功消息，包含会话信息
        String welcomeMessage = String.format("{\"type\":\"connection_established\",\"sessionId\":\"%s\",\"ip\":\"%s\",\"timestamp\":%d,\"message\":\"连接成功，专属服务已启动\"}",
                sessionId, ipAddress, System.currentTimeMillis());
        session.sendMessage(new TextMessage(welcomeMessage));

        log.info("为客户端创建专属连接服务 - IP: {}, SessionId: {}", ipAddress, sessionId);
    }
    
    /**
     * 接收到消息时调用
     */
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();

        // 处理不同类型的消息
        if (message instanceof TextMessage) {
            String payload = message.getPayload().toString();
            log.info("收到文本消息 - SessionId: {}, Message: {}", sessionId, payload);

            // 更新心跳时间
            UserSession userSession = userSessionManager.getSession(sessionId);
            if (userSession != null) {
                userSession.updateHeartbeat();
            }

            // 处理不同类型的消息
            if ("ping".equals(payload)) {
                // 客户端主动心跳检测
                session.sendMessage(new TextMessage("pong"));
            } else if ("pong".equals(payload)) {
                // 客户端响应服务器心跳
                heartbeatService.handlePongMessage(sessionId);
            } else if ("disconnect".equals(payload) || "close".equals(payload)) {
                // 客户端主动请求断开连接
                handleClientDisconnectRequest(session, sessionId);
            } else if (payload.startsWith("{") && payload.contains("\"type\"")) {
                // 处理JSON格式的消息
                handleJsonMessage(session, sessionId, payload);
            } else {
                // 回显消息
                String response = String.format("{\"type\":\"echo\",\"sessionId\":\"%s\",\"originalMessage\":\"%s\",\"timestamp\":%d}",
                        sessionId, payload, System.currentTimeMillis());
                session.sendMessage(new TextMessage(response));
            }
        } else if (message instanceof PongMessage) {
            // 处理 WebSocket 协议级别的 pong 消息
            log.debug("收到 Pong 消息 - SessionId: {}", sessionId);
            heartbeatService.handlePongMessage(sessionId);
        }
    }
    
    /**
     * 传输错误时调用
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        log.error("WebSocket传输错误 - SessionId: {}, Error: {}", sessionId, exception.getMessage());
        
        // 移除会话
        userSessionManager.removeSession(sessionId);
    }
    
    /**
     * 连接关闭后调用
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        log.info("WebSocket连接关闭 - SessionId: {}, CloseStatus: {}", sessionId, closeStatus);
        
        // 移除会话
        userSessionManager.removeSession(sessionId);
    }
    
    /**
     * 是否支持部分消息
     */
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(WebSocketSession session) {
        // 尝试从各种请求头中获取真实IP
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = getHeaderValue(session, headerName);
            if (isValidIp(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        // 如果都没有，使用远程地址
        return session.getRemoteAddress() != null ? 
               session.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }
    
    /**
     * 获取请求头值
     */
    private String getHeaderValue(WebSocketSession session, String headerName) {
        if (session.getHandshakeHeaders().containsKey(headerName)) {
            return session.getHandshakeHeaders().getFirst(headerName);
        }
        return null;
    }
    
    /**
     * 验证IP地址是否有效
     */
    private boolean isValidIp(String ip) {
        return ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip);
    }

    /**
     * 处理客户端主动断开请求
     */
    private void handleClientDisconnectRequest(WebSocketSession session, String sessionId) {
        try {
            UserSession userSession = userSessionManager.getSession(sessionId);
            if (userSession != null) {
                String ipAddress = userSession.getIpAddress();
                log.info("客户端主动请求断开连接 - IP: {}, SessionId: {}", ipAddress, sessionId);

                // 发送断开确认消息
                String disconnectMessage = String.format("{\"type\":\"disconnect_confirmed\",\"sessionId\":\"%s\",\"message\":\"连接即将关闭，专属服务已清理\",\"timestamp\":%d}",
                        sessionId, System.currentTimeMillis());
                session.sendMessage(new TextMessage(disconnectMessage));

                // 延迟关闭连接，确保消息发送完成
                new Thread(() -> {
                    try {
                        Thread.sleep(100); // 等待100ms确保消息发送
                        session.close();
                    } catch (Exception e) {
                        log.error("关闭连接时出错: {}", e.getMessage());
                    }
                }).start();
            }
        } catch (Exception e) {
            log.error("处理客户端断开请求时出错: {}", e.getMessage());
        }
    }

    /**
     * 处理JSON格式的消息
     */
    private void handleJsonMessage(WebSocketSession session, String sessionId, String payload) {
        try {
            // 这里可以根据需要解析JSON消息并处理
            // 例如：{"type":"custom_command","data":"some_data"}

            log.info("收到JSON消息 - SessionId: {}, Message: {}", sessionId, payload);

            // 简单的JSON响应
            String response = String.format("{\"type\":\"json_response\",\"sessionId\":\"%s\",\"received\":true,\"timestamp\":%d}",
                    sessionId, System.currentTimeMillis());
            session.sendMessage(new TextMessage(response));

        } catch (Exception e) {
            log.error("处理JSON消息时出错: {}", e.getMessage());
            try {
                String errorResponse = String.format("{\"type\":\"error\",\"sessionId\":\"%s\",\"message\":\"消息处理失败\",\"timestamp\":%d}",
                        sessionId, System.currentTimeMillis());
                session.sendMessage(new TextMessage(errorResponse));
            } catch (Exception ex) {
                log.error("发送错误响应失败: {}", ex.getMessage());
            }
        }
    }
}
