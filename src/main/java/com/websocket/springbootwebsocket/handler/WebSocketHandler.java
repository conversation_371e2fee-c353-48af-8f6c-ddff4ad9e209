package com.websocket.springbootwebsocket.handler;

import com.websocket.springbootwebsocket.entity.UserSession;
import com.websocket.springbootwebsocket.manager.UserSessionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

/**
 * WebSocket处理器 - 处理连接、断开和消息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketHandler implements org.springframework.web.socket.WebSocketHandler {
    
    private final UserSessionManager userSessionManager;
    
    /**
     * 连接建立后调用
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        String ipAddress = getClientIpAddress(session);
        
        // 创建用户会话并添加到管理器
        UserSession userSession = new UserSession(sessionId, ipAddress, session);
        userSessionManager.addSession(userSession);
        
        // 向客户端发送连接成功消息
        session.sendMessage(new TextMessage("连接成功! SessionId: " + sessionId + ", IP: " + ipAddress));
    }
    
    /**
     * 接收到消息时调用
     */
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload().toString();
        
        log.info("收到消息 - SessionId: {}, Message: {}", sessionId, payload);
        
        // 可以根据需要处理不同类型的消息
        if ("ping".equals(payload)) {
            // 心跳检测
            session.sendMessage(new TextMessage("pong"));
        } else {
            // 回显消息
            session.sendMessage(new TextMessage("服务器收到: " + payload));
        }
    }
    
    /**
     * 传输错误时调用
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        log.error("WebSocket传输错误 - SessionId: {}, Error: {}", sessionId, exception.getMessage());
        
        // 移除会话
        userSessionManager.removeSession(sessionId);
    }
    
    /**
     * 连接关闭后调用
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        log.info("WebSocket连接关闭 - SessionId: {}, CloseStatus: {}", sessionId, closeStatus);
        
        // 移除会话
        userSessionManager.removeSession(sessionId);
    }
    
    /**
     * 是否支持部分消息
     */
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(WebSocketSession session) {
        // 尝试从各种请求头中获取真实IP
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = getHeaderValue(session, headerName);
            if (isValidIp(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        // 如果都没有，使用远程地址
        return session.getRemoteAddress() != null ? 
               session.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }
    
    /**
     * 获取请求头值
     */
    private String getHeaderValue(WebSocketSession session, String headerName) {
        if (session.getHandshakeHeaders().containsKey(headerName)) {
            return session.getHandshakeHeaders().getFirst(headerName);
        }
        return null;
    }
    
    /**
     * 验证IP地址是否有效
     */
    private boolean isValidIp(String ip) {
        return ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip);
    }
}
