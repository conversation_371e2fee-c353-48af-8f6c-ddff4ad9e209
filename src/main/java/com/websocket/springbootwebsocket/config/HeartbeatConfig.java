package com.websocket.springbootwebsocket.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 心跳检测配置
 */
@Configuration
@EnableScheduling
public class HeartbeatConfig {
    
    /**
     * 心跳间隔时间（秒）
     */
    public static final int HEARTBEAT_INTERVAL = 30;
    
    /**
     * 心跳超时时间（秒）
     */
    public static final int HEARTBEAT_TIMEOUT = 60;
    
    /**
     * 清理无效连接间隔时间（秒）
     */
    public static final int CLEANUP_INTERVAL = 60;
}
