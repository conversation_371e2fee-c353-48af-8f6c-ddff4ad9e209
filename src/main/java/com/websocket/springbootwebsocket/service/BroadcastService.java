package com.websocket.springbootwebsocket.service;

import com.websocket.springbootwebsocket.entity.UserSession;
import com.websocket.springbootwebsocket.manager.UserSessionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 广播通知服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BroadcastService {
    
    private final UserSessionManager userSessionManager;
    
    /**
     * 广播用户连接事件
     */
    public void broadcastUserConnected(UserSession connectedUser) {
        String message = String.format("{\"type\":\"user_connected\",\"ip\": \"%s\",\"sessionId\":\"%s\",\"connectTime\":\"%s\",\"onlineCount\":%d,\"timestamp\":%d}",
                connectedUser.getIpAddress(),
                connectedUser.getSessionId(),
                connectedUser.getConnectTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                userSessionManager.getOnlineCount(),
                System.currentTimeMillis());
        
        broadcastToAll(message, connectedUser.getSessionId());
        log.info("广播用户连接事件: IP={}, 当前在线数={}", connectedUser.getIpAddress(), userSessionManager.getOnlineCount());
    }
    
    /**
     * 广播用户断开事件
     */
    public void broadcastUserDisconnected(String disconnectedIp, String sessionId) {
        String message = String.format("{\"type\":\"user_disconnected\",\"ip\":\"%s\",\"sessionId\":\"%s\",\"disconnectTime\":\"%s\",\"onlineCount\":%d,\"timestamp\":%d}",
                disconnectedIp,
                sessionId,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                userSessionManager.getOnlineCount(),
                System.currentTimeMillis());
        
        broadcastToAll(message, null);
        log.info("广播用户断开事件: IP={}, 当前在线数={}", disconnectedIp, userSessionManager.getOnlineCount());
    }
    
    /**
     * 广播心跳超时事件
     */
    public void broadcastHeartbeatTimeout(String timeoutIp, String sessionId) {
        String message = String.format("{\"type\":\"heartbeat_timeout\",\"ip\":\"%s\",\"sessionId\":\"%s\",\"timeoutTime\":\"%s\",\"onlineCount\":%d,\"timestamp\":%d}",
                timeoutIp,
                sessionId,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                userSessionManager.getOnlineCount(),
                System.currentTimeMillis());
        
        broadcastToAll(message, null);
        log.info("广播心跳超时事件: IP={}, 当前在线数={}", timeoutIp, userSessionManager.getOnlineCount());
    }
    
    /**
     * 广播服务器状态
     */
    public void broadcastServerStatus() {
        String message = String.format("{\"type\":\"server_status\",\"onlineCount\":%d,\"uniqueIpCount\":%d,\"serverTime\":\"%s\",\"timestamp\":%d}",
                userSessionManager.getOnlineCount(),
                userSessionManager.getAllActiveIpAddresses().size(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                System.currentTimeMillis());
        
        broadcastToAll(message, null);
    }
    
    /**
     * 向所有客户端广播消息
     */
    private void broadcastToAll(String message, String excludeSessionId) {
        List<UserSession> activeSessions = userSessionManager.getAllActiveSessions();
        int successCount = 0;
        int failCount = 0;
        
        for (UserSession session : activeSessions) {
            // 排除指定的会话（比如不向刚连接的用户发送连接通知）
            if (excludeSessionId != null && excludeSessionId.equals(session.getSessionId())) {
                continue;
            }
            
            try {
                if (session.isActive()) {
                    session.getWebSocketSession().sendMessage(new TextMessage(message));
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.warn("广播消息失败 - SessionId: {}, IP: {}, Error: {}", 
                        session.getSessionId(), session.getIpAddress(), e.getMessage());
                failCount++;
                // 标记会话为无效，等待清理
                userSessionManager.removeSession(session.getSessionId());
            }
        }
        
        log.debug("广播完成 - 成功: {}, 失败: {}", successCount, failCount);
    }
    
    /**
     * 向指定用户发送消息
     */
    public boolean sendToUser(String sessionId, String message) {
        UserSession session = userSessionManager.getSession(sessionId);
        if (session != null && session.isActive()) {
            try {
                session.getWebSocketSession().sendMessage(new TextMessage(message));
                return true;
            } catch (Exception e) {
                log.warn("发送消息失败 - SessionId: {}, Error: {}", sessionId, e.getMessage());
                userSessionManager.removeSession(sessionId);
            }
        }
        return false;
    }
}
