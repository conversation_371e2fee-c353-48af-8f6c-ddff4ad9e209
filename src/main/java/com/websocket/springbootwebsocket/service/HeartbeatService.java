package com.websocket.springbootwebsocket.service;

import com.websocket.springbootwebsocket.config.HeartbeatConfig;
import com.websocket.springbootwebsocket.entity.UserSession;
import com.websocket.springbootwebsocket.manager.UserSessionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.PingMessage;
import org.springframework.web.socket.TextMessage;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 心跳检测服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HeartbeatService {
    
    private final UserSessionManager userSessionManager;
    private final BroadcastService broadcastService;
    
    /**
     * 定时发送心跳检测
     * 每30秒执行一次
     */
    @Scheduled(fixedRate = HeartbeatConfig.HEARTBEAT_INTERVAL * 1000)
    public void sendHeartbeat() {
        List<UserSession> activeSessions = userSessionManager.getAllActiveSessions();
        if (activeSessions.isEmpty()) {
            return;
        }
        
        log.debug("开始心跳检测，当前在线用户数: {}", activeSessions.size());
        
        int pingCount = 0;
        int timeoutCount = 0;
        List<String> timeoutSessions = new CopyOnWriteArrayList<>();
        
        for (UserSession session : activeSessions) {
            try {
                // 检查是否心跳超时
                if (session.isHeartbeatTimeout(HeartbeatConfig.HEARTBEAT_TIMEOUT)) {
                    log.warn("用户心跳超时 - IP: {}, SessionId: {}", session.getIpAddress(), session.getSessionId());
                    timeoutSessions.add(session.getSessionId());
                    timeoutCount++;
                    continue;
                }
                
                // 发送心跳检测
                if (session.isActive()) {
                    // 发送 ping 消息
                    session.getWebSocketSession().sendMessage(new PingMessage());
                    session.setWaitingForPong(true);
                    pingCount++;
                    
                    log.debug("发送心跳 - IP: {}, SessionId: {}", session.getIpAddress(), session.getSessionId());
                }
            } catch (Exception e) {
                log.error("心跳检测异常 - SessionId: {}, Error: {}", session.getSessionId(), e.getMessage());
                timeoutSessions.add(session.getSessionId());
            }
        }
        
        // 处理超时的会话
        for (String sessionId : timeoutSessions) {
            UserSession timeoutSession = userSessionManager.getSession(sessionId);
            if (timeoutSession != null) {
                String timeoutIp = timeoutSession.getIpAddress();
                userSessionManager.removeSession(sessionId);
                broadcastService.broadcastHeartbeatTimeout(timeoutIp, sessionId);
            }
        }
        
        if (pingCount > 0 || timeoutCount > 0) {
            log.info("心跳检测完成 - 发送心跳: {}, 超时移除: {}, 当前在线: {}", 
                    pingCount, timeoutCount, userSessionManager.getOnlineCount());
        }
    }
    
    /**
     * 定时清理无效连接
     * 每60秒执行一次
     */
    @Scheduled(fixedRate = HeartbeatConfig.CLEANUP_INTERVAL * 1000)
    public void cleanupInactiveSessions() {
        log.debug("开始清理无效连接");
        
        List<UserSession> allSessions = userSessionManager.getAllActiveSessions();
        List<String> inactiveSessions = new CopyOnWriteArrayList<>();
        
        for (UserSession session : allSessions) {
            if (!session.isActive()) {
                inactiveSessions.add(session.getSessionId());
            }
        }
        
        int cleanupCount = 0;
        for (String sessionId : inactiveSessions) {
            UserSession inactiveSession = userSessionManager.getSession(sessionId);
            if (inactiveSession != null) {
                String inactiveIp = inactiveSession.getIpAddress();
                userSessionManager.removeSession(sessionId);
                broadcastService.broadcastUserDisconnected(inactiveIp, sessionId);
                cleanupCount++;
            }
        }
        
        if (cleanupCount > 0) {
            log.info("清理无效连接完成 - 清理数量: {}, 当前在线: {}", cleanupCount, userSessionManager.getOnlineCount());
        }
    }
    
    /**
     * 定时广播服务器状态
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void broadcastServerStatus() {
        int onlineCount = userSessionManager.getOnlineCount();
        if (onlineCount > 0) {
            broadcastService.broadcastServerStatus();
            log.debug("广播服务器状态 - 在线用户数: {}", onlineCount);
        }
    }
    
    /**
     * 处理客户端的 pong 响应
     */
    public void handlePongMessage(String sessionId) {
        UserSession session = userSessionManager.getSession(sessionId);
        if (session != null) {
            session.updateHeartbeat();
            log.debug("收到心跳响应 - IP: {}, SessionId: {}", session.getIpAddress(), sessionId);
        }
    }
    
    /**
     * 手动触发心跳检测（用于测试）
     */
    public void triggerHeartbeatCheck() {
        log.info("手动触发心跳检测");
        sendHeartbeat();
    }
    
    /**
     * 手动触发清理无效连接（用于测试）
     */
    public void triggerCleanup() {
        log.info("手动触发清理无效连接");
        cleanupInactiveSessions();
    }
}
