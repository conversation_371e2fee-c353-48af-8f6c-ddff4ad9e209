package com.websocket.springbootwebsocket.manager;

import com.websocket.springbootwebsocket.entity.UserSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 用户会话管理器 - 内存中管理所有活跃的用户会话
 */
@Slf4j
@Component
public class UserSessionManager {

    @Autowired
    @Lazy
    private com.websocket.springbootwebsocket.service.BroadcastService broadcastService;
    
    /**
     * 存储所有活跃的用户会话 - 使用线程安全的ConcurrentHashMap
     * Key: sessionId, Value: UserSession
     */
    private final ConcurrentHashMap<String, UserSession> activeSessions = new ConcurrentHashMap<>();
    
    /**
     * 添加用户会话 - 为每个客户端创建专属连接服务
     */
    public void addSession(UserSession userSession) {
        activeSessions.put(userSession.getSessionId(), userSession);
        log.info("创建专属连接服务 - IP: {}, SessionId: {}, 当前活跃连接数: {}",
                userSession.getIpAddress(), userSession.getSessionId(), getOnlineCount());

        // 广播用户连接事件
        if (broadcastService != null) {
            broadcastService.broadcastUserConnected(userSession);
        }
    }
    
    /**
     * 移除用户会话 - 清理专属连接服务
     */
    public void removeSession(String sessionId) {
        UserSession removedSession = activeSessions.remove(sessionId);
        if (removedSession != null) {
            log.info("清理专属连接服务 - IP: {}, SessionId: {}, 剩余活跃连接数: {}",
                    removedSession.getIpAddress(), sessionId, getOnlineCount());

            // 广播用户断开事件
            if (broadcastService != null) {
                broadcastService.broadcastUserDisconnected(removedSession.getIpAddress(), sessionId);
            }
        }
    }

    /**
     * 强制断开指定会话
     */
    public boolean forceDisconnectSession(String sessionId) {
        UserSession session = activeSessions.get(sessionId);
        if (session != null && session.isActive()) {
            try {
                session.getWebSocketSession().close();
                removeSession(sessionId);
                log.info("强制断开连接成功 - SessionId: {}", sessionId);
                return true;
            } catch (Exception e) {
                log.error("强制断开连接失败 - SessionId: {}, Error: {}", sessionId, e.getMessage());
                // 即使关闭失败，也要从管理器中移除
                removeSession(sessionId);
                return false;
            }
        }
        return false;
    }
    
    /**
     * 根据SessionId获取用户会话
     */
    public UserSession getSession(String sessionId) {
        return activeSessions.get(sessionId);
    }
    
    /**
     * 获取所有活跃的用户会话
     */
    public List<UserSession> getAllActiveSessions() {
        // 过滤掉已经断开的会话
        return activeSessions.values().stream()
                .filter(UserSession::isActive)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有在线用户的IP地址列表
     */
    public List<String> getAllActiveIpAddresses() {
        return activeSessions.values().stream()
                .filter(UserSession::isActive)
                .map(UserSession::getIpAddress)
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 获取在线用户数量
     */
    public int getOnlineCount() {
        return (int) activeSessions.values().stream()
                .filter(UserSession::isActive)
                .count();
    }
    
    /**
     * 清理已断开的会话
     */
    public void cleanupInactiveSessions() {
        List<String> inactiveSessionIds = activeSessions.entrySet().stream()
                .filter(entry -> !entry.getValue().isActive())
                .map(entry -> entry.getKey())
                .collect(Collectors.toList());
        
        inactiveSessionIds.forEach(sessionId -> {
            activeSessions.remove(sessionId);
            log.info("清理无效会话: SessionId={}", sessionId);
        });
    }
    
    /**
     * 根据IP地址获取用户会话列表
     */
    public List<UserSession> getSessionsByIp(String ipAddress) {
        return activeSessions.values().stream()
                .filter(session -> session.isActive() && ipAddress.equals(session.getIpAddress()))
                .collect(Collectors.toList());
    }
}
