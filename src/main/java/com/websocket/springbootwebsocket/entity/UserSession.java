package com.websocket.springbootwebsocket.entity;

import lombok.Data;
import org.springframework.web.socket.WebSocketSession;

import java.time.LocalDateTime;

/**
 * 用户会话实体类
 */
@Data
public class UserSession {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 用户IP地址
     */
    private String ipAddress;
    
    /**
     * 连接时间
     */
    private LocalDateTime connectTime;
    
    /**
     * WebSocket会话对象
     */
    private WebSocketSession webSocketSession;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    public UserSession(String sessionId, String ipAddress, WebSocketSession webSocketSession) {
        this.sessionId = sessionId;
        this.ipAddress = ipAddress;
        this.webSocketSession = webSocketSession;
        this.connectTime = LocalDateTime.now();
        this.userAgent = getHeaderValue(webSocketSession, "User-Agent");
    }
    
    /**
     * 获取请求头信息
     */
    private String getHeaderValue(WebSocketSession session, String headerName) {
        if (session.getHandshakeHeaders().containsKey(headerName)) {
            return session.getHandshakeHeaders().getFirst(headerName);
        }
        return "";
    }
    
    /**
     * 检查会话是否还活跃
     */
    public boolean isActive() {
        return webSocketSession != null && webSocketSession.isOpen();
    }
}
