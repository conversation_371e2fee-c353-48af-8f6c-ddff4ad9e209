package com.websocket.springbootwebsocket.controller;

import com.websocket.springbootwebsocket.entity.UserSession;
import com.websocket.springbootwebsocket.manager.UserSessionManager;
import com.websocket.springbootwebsocket.service.HeartbeatService;
import com.websocket.springbootwebsocket.service.BroadcastService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息控制器 - 提供HTTP接口查询用户信息
 */
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@CrossOrigin(origins = "*") // 允许跨域访问
public class UserController {

    private final UserSessionManager userSessionManager;
    private final HeartbeatService heartbeatService;
    private final BroadcastService broadcastService;
    
    /**
     * 获取所有在线用户的IP地址列表
     */
    @GetMapping("/online-ips")
    public ResponseEntity<Map<String, Object>> getOnlineIps() {
        List<String> ipList = userSessionManager.getAllActiveIpAddresses();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("count", ipList.size());
        response.put("ips", ipList);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取在线用户数量
     */
    @GetMapping("/online-count")
    public ResponseEntity<Map<String, Object>> getOnlineCount() {
        int count = userSessionManager.getOnlineCount();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("count", count);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有在线用户详细信息
     */
    @GetMapping("/online-users")
    public ResponseEntity<Map<String, Object>> getOnlineUsers() {
        List<UserSession> sessions = userSessionManager.getAllActiveSessions();
        
        // 转换为前端友好的格式
        List<Map<String, Object>> userList = sessions.stream()
                .map(this::convertToUserInfo)
                .collect(Collectors.toList());
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("count", userList.size());
        response.put("users", userList);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 根据IP地址查询用户信息
     */
    @GetMapping("/by-ip/{ip}")
    public ResponseEntity<Map<String, Object>> getUsersByIp(@PathVariable String ip) {
        List<UserSession> sessions = userSessionManager.getSessionsByIp(ip);
        
        List<Map<String, Object>> userList = sessions.stream()
                .map(this::convertToUserInfo)
                .collect(Collectors.toList());
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("count", userList.size());
        response.put("users", userList);
        response.put("ip", ip);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取服务器状态信息
     */
    @GetMapping("/server-status")
    public ResponseEntity<Map<String, Object>> getServerStatus() {
        // 清理无效会话
        userSessionManager.cleanupInactiveSessions();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("onlineCount", userSessionManager.getOnlineCount());
        response.put("uniqueIpCount", userSessionManager.getAllActiveIpAddresses().size());
        response.put("serverTime", System.currentTimeMillis());
        response.put("status", "running");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 手动触发心跳检测
     */
    @PostMapping("/trigger-heartbeat")
    public ResponseEntity<Map<String, Object>> triggerHeartbeat() {
        heartbeatService.triggerHeartbeatCheck();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "心跳检测已触发");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 手动触发清理无效连接
     */
    @PostMapping("/trigger-cleanup")
    public ResponseEntity<Map<String, Object>> triggerCleanup() {
        heartbeatService.triggerCleanup();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "清理无效连接已触发");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 手动广播服务器状态
     */
    @PostMapping("/broadcast-status")
    public ResponseEntity<Map<String, Object>> broadcastStatus() {
        broadcastService.broadcastServerStatus();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "服务器状态已广播");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 获取服务器网络信息
     */
    @GetMapping("/network-info")
    public ResponseEntity<Map<String, Object>> getNetworkInfo() {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, String>> networkInterfaces = new ArrayList<>();

        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();

                if (networkInterface.isUp() && !networkInterface.isLoopback()) {
                    Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress address = addresses.nextElement();
                        if (!address.isLoopbackAddress() && address.isSiteLocalAddress()) {
                            Map<String, String> interfaceInfo = new HashMap<>();
                            interfaceInfo.put("name", networkInterface.getDisplayName());
                            interfaceInfo.put("ip", address.getHostAddress());
                            interfaceInfo.put("websocketUrl", "ws://" + address.getHostAddress() + ":8888/websocket");
                            networkInterfaces.add(interfaceInfo);
                        }
                    }
                }
            }

            response.put("success", true);
            response.put("networkInterfaces", networkInterfaces);
            response.put("serverPort", 8888);
            response.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 将UserSession转换为前端友好的格式
     */
    private Map<String, Object> convertToUserInfo(UserSession session) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("sessionId", session.getSessionId());
        userInfo.put("ipAddress", session.getIpAddress());
        userInfo.put("connectTime", session.getConnectTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        userInfo.put("lastHeartbeat", session.getLastHeartbeat().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        userInfo.put("userAgent", session.getUserAgent());
        userInfo.put("isActive", session.isActive());
        userInfo.put("waitingForPong", session.isWaitingForPong());
        return userInfo;
    }
}
