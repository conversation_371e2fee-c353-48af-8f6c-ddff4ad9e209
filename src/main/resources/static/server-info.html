<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器信息</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .ip-address {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
        }
        .url-box {
            background: #f8f9fa;
            border: 2px dashed #007bff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .copy-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background-color: #218838;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .loading { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 WebSocket服务器信息</h1>
        <p>请将以下信息发送给需要连接的用户：</p>
        
        <div id="loading" class="loading">正在获取服务器信息...</div>
        
        <div id="serverInfo" style="display: none;">
            <h3>📍 服务器网络地址</h3>
            <div id="networkInterfaces"></div>
            
            <h3>🔗 连接地址</h3>
            <p><strong>测试页面地址：</strong></p>
            <div id="testUrls"></div>
            
            <p><strong>WebSocket连接地址（给Unity开发者）：</strong></p>
            <div id="wsUrls"></div>
            
            <p><strong>API接口地址：</strong></p>
            <div id="apiUrls"></div>
        </div>
        
        <div id="error" style="display: none;" class="error">
            <h3>❌ 获取信息失败</h3>
            <p>请确保服务器正在运行，然后刷新页面重试。</p>
        </div>
    </div>

    <script>
        window.onload = function() {
            getServerInfo();
        };
        
        function getServerInfo() {
            fetch('/api/users/network-info')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayServerInfo(data);
                    } else {
                        showError('获取服务器信息失败: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('网络请求失败: ' + error.message);
                });
        }
        
        function displayServerInfo(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('serverInfo').style.display = 'block';
            
            const networkDiv = document.getElementById('networkInterfaces');
            const testUrlsDiv = document.getElementById('testUrls');
            const wsUrlsDiv = document.getElementById('wsUrls');
            const apiUrlsDiv = document.getElementById('apiUrls');
            
            let networkHtml = '';
            let testUrlsHtml = '';
            let wsUrlsHtml = '';
            let apiUrlsHtml = '';
            
            data.networkInterfaces.forEach(interface => {
                networkHtml += `
                    <div class="ip-address">
                        ${interface.name}: ${interface.ip}
                    </div>
                `;
                
                testUrlsHtml += `
                    <div class="url-box">
                        http://${interface.ip}:8888/test.html
                        <button class="copy-btn" onclick="copyToClipboard('http://${interface.ip}:8888/test.html')">复制</button>
                    </div>
                `;
                
                wsUrlsHtml += `
                    <div class="url-box">
                        ws://${interface.ip}:8888/websocket
                        <button class="copy-btn" onclick="copyToClipboard('ws://${interface.ip}:8888/websocket')">复制</button>
                    </div>
                `;
                
                apiUrlsHtml += `
                    <div class="url-box">
                        http://${interface.ip}:8888/api/users/
                        <button class="copy-btn" onclick="copyToClipboard('http://${interface.ip}:8888/api/users/')">复制</button>
                    </div>
                `;
            });
            
            networkDiv.innerHTML = networkHtml;
            testUrlsDiv.innerHTML = testUrlsHtml;
            wsUrlsDiv.innerHTML = wsUrlsHtml;
            apiUrlsDiv.innerHTML = apiUrlsHtml;
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML += `<p>${message}</p>`;
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // 临时显示复制成功提示
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '已复制!';
                btn.style.backgroundColor = '#28a745';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.backgroundColor = '#28a745';
                }, 2000);
            }).catch(function(err) {
                alert('复制失败，请手动复制: ' + text);
            });
        }
    </script>
</body>
</html>
