<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unity客户端接入指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .endpoint {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
        }
        .method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background-color: #28a745; }
        .post { background-color: #007bff; }
        .websocket { background-color: #6f42c1; }
        h1, h2, h3 { color: #333; }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Unity客户端WebSocket接入指南</h1>
        
        <div class="success">
            <h3>✅ 服务器特性</h3>
            <ul>
                <li><strong>专属连接服务</strong>：每个客户端连接都有独立的服务实例</li>
                <li><strong>实时响应</strong>：客户端断开时立即清理连接资源</li>
                <li><strong>多种断开方式</strong>：支持客户端主动断开和服务器手动断开</li>
                <li><strong>JSON消息格式</strong>：结构化的消息通信</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🔌 WebSocket连接</h2>
        
        <div class="endpoint">
            <span class="method websocket">WebSocket</span>
            <strong>ws://服务器IP:8888/websocket</strong>
        </div>
        
        <h3>连接建立</h3>
        <p>连接成功后，服务器会发送欢迎消息：</p>
        <div class="code-block">
{
  "type": "connection_established",
  "sessionId": "会话ID",
  "ip": "客户端IP",
  "timestamp": 时间戳,
  "message": "连接成功，专属服务已启动"
}
        </div>
        
        <div class="warning">
            <strong>重要：</strong>请保存返回的 <code>sessionId</code>，后续的手动断开操作需要使用此ID。
        </div>
    </div>

    <div class="container">
        <h2>💬 消息通信</h2>
        
        <h3>发送消息类型</h3>
        
        <h4>1. 心跳检测</h4>
        <div class="code-block">
// 发送
"ping"

// 服务器响应
"pong"
        </div>
        
        <h4>2. 主动断开连接</h4>
        <div class="code-block">
// 发送
"disconnect" 或 "close"

// 服务器响应
{
  "type": "disconnect_confirmed",
  "sessionId": "会话ID",
  "message": "连接即将关闭，专属服务已清理",
  "timestamp": 时间戳
}
        </div>
        
        <h4>3. JSON格式消息</h4>
        <div class="code-block">
// 发送
{
  "type": "custom_command",
  "data": "自定义数据"
}

// 服务器响应
{
  "type": "json_response",
  "sessionId": "会话ID",
  "received": true,
  "timestamp": 时间戳
}
        </div>
        
        <h4>4. 普通文本消息</h4>
        <div class="code-block">
// 发送
"Hello Server"

// 服务器响应
{
  "type": "echo",
  "sessionId": "会话ID",
  "originalMessage": "Hello Server",
  "timestamp": 时间戳
}
        </div>
    </div>

    <div class="container">
        <h2>🔌 HTTP API接口</h2>
        
        <h3>手动断开连接</h3>
        <div class="endpoint">
            <span class="method post">POST</span>
            <strong>/api/users/disconnect/{sessionId}</strong>
        </div>
        <p>使用连接时获得的sessionId来手动断开指定连接。</p>
        
        <div class="code-block">
// 响应示例
{
  "success": true,
  "message": "连接已断开",
  "sessionId": "会话ID",
  "ip": "客户端IP",
  "timestamp": 时间戳
}
        </div>
        
        <h3>批量断开（按IP）</h3>
        <div class="endpoint">
            <span class="method post">POST</span>
            <strong>/api/users/disconnect-by-ip/{ip}</strong>
        </div>
        
        <h3>获取会话信息</h3>
        <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/api/users/session/{sessionId}</strong>
        </div>
        
        <h3>获取在线用户列表</h3>
        <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/api/users/online-ips</strong>
        </div>
        
        <h3>获取在线用户数量</h3>
        <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/api/users/online-count</strong>
        </div>
    </div>

    <div class="container">
        <h2>📝 Unity C# 示例代码</h2>
        
        <div class="code-block">
using System;
using UnityEngine;
using WebSocketSharp;

public class WebSocketClient : MonoBehaviour
{
    private WebSocket ws;
    private string sessionId;
    
    void Start()
    {
        // 替换为实际的服务器IP
        string serverUrl = "ws://*************:8888/websocket";
        
        ws = new WebSocket(serverUrl);
        
        ws.OnOpen += (sender, e) =>
        {
            Debug.Log("WebSocket连接已建立");
        };
        
        ws.OnMessage += (sender, e) =>
        {
            Debug.Log("收到消息: " + e.Data);
            
            // 解析JSON消息
            try
            {
                var message = JsonUtility.FromJson&lt;ServerMessage&gt;(e.Data);
                if (message.type == "connection_established")
                {
                    sessionId = message.sessionId;
                    Debug.Log("会话ID: " + sessionId);
                }
            }
            catch (Exception ex)
            {
                Debug.Log("消息解析失败: " + ex.Message);
            }
        };
        
        ws.OnClose += (sender, e) =>
        {
            Debug.Log("WebSocket连接已关闭");
        };
        
        ws.OnError += (sender, e) =>
        {
            Debug.LogError("WebSocket错误: " + e.Message);
        };
        
        ws.Connect();
    }
    
    // 发送心跳
    public void SendPing()
    {
        if (ws != null && ws.ReadyState == WebSocketState.Open)
        {
            ws.Send("ping");
        }
    }
    
    // 主动断开连接
    public void DisconnectFromServer()
    {
        if (ws != null && ws.ReadyState == WebSocketState.Open)
        {
            ws.Send("disconnect");
        }
    }
    
    void OnDestroy()
    {
        if (ws != null)
        {
            ws.Close();
        }
    }
}

[System.Serializable]
public class ServerMessage
{
    public string type;
    public string sessionId;
    public string ip;
    public long timestamp;
    public string message;
}
        </div>
    </div>

    <div class="container">
        <h2>⚠️ 注意事项</h2>
        
        <ul>
            <li><strong>连接管理</strong>：每个连接都是独立的服务实例，断开时会立即清理资源</li>
            <li><strong>会话ID</strong>：连接建立后请保存sessionId，用于后续的手动断开操作</li>
            <li><strong>断开方式</strong>：支持客户端发送"disconnect"消息或调用HTTP API断开</li>
            <li><strong>消息格式</strong>：建议使用JSON格式进行结构化通信</li>
            <li><strong>错误处理</strong>：请妥善处理连接异常和消息解析错误</li>
            <li><strong>网络环境</strong>：确保客户端和服务器在同一网络中，或正确配置防火墙</li>
        </ul>
    </div>
</body>
</html>
