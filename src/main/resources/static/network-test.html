<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .result {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>🔧 网络连接诊断工具</h1>
    
    <div class="container">
        <h3>📍 服务器信息</h3>
        <div id="serverInfo">
            <p><strong>当前页面地址：</strong><span id="currentUrl"></span></p>
            <p><strong>建议的服务器IP：</strong><span id="serverIp"></span></p>
            <p><strong>WebSocket地址：</strong><span id="wsUrl"></span></p>
        </div>
    </div>
    
    <div class="container">
        <h3>🌐 连接测试</h3>
        <div>
            <label>服务器IP地址：</label>
            <input type="text" id="testIp" placeholder="例如: *************">
            <button onclick="testConnection()">测试连接</button>
            <button onclick="testWebSocket()">测试WebSocket</button>
        </div>
        <div id="connectionResult" class="result"></div>
    </div>
    
    <div class="container">
        <h3>🔍 网络诊断</h3>
        <button onclick="runDiagnostics()">运行完整诊断</button>
        <button onclick="getNetworkInfo()">获取网络信息</button>
        <div id="diagnosticsResult" class="result"></div>
    </div>
    
    <div class="container">
        <h3>📋 解决方案</h3>
        <div id="solutions">
            <h4>常见问题和解决方案：</h4>
            <ol>
                <li><strong>防火墙阻止：</strong>
                    <ul>
                        <li>Windows: 控制面板 → 系统和安全 → Windows Defender 防火墙 → 允许应用通过防火墙</li>
                        <li>添加端口8888的入站规则</li>
                    </ul>
                </li>
                <li><strong>网络配置：</strong>
                    <ul>
                        <li>确保两台电脑在同一网络中</li>
                        <li>检查路由器是否阻止了端口8888</li>
                    </ul>
                </li>
                <li><strong>服务器配置：</strong>
                    <ul>
                        <li>确保服务器绑定到0.0.0.0而不是127.0.0.1</li>
                        <li>检查application.properties配置</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        window.onload = function() {
            initializeServerInfo();
        };
        
        function initializeServerInfo() {
            const currentUrl = window.location.href;
            const hostname = window.location.hostname;
            const port = window.location.port || '8888';
            
            document.getElementById('currentUrl').textContent = currentUrl;
            document.getElementById('serverIp').textContent = hostname;
            document.getElementById('wsUrl').textContent = `ws://${hostname}:${port}/websocket`;
            document.getElementById('testIp').value = hostname;
        }
        
        function testConnection() {
            const ip = document.getElementById('testIp').value.trim();
            if (!ip) {
                showResult('connectionResult', '请输入服务器IP地址', 'error');
                return;
            }
            
            showResult('connectionResult', '正在测试HTTP连接...', 'info');
            
            // 测试HTTP连接
            const testUrl = `http://${ip}:8888/api/users/server-status`;
            
            fetch(testUrl)
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                })
                .then(data => {
                    showResult('connectionResult', 
                        `✅ HTTP连接成功！\n服务器状态：${JSON.stringify(data, null, 2)}`, 'success');
                })
                .catch(error => {
                    showResult('connectionResult', 
                        `❌ HTTP连接失败：${error.message}\n\n可能的原因：\n1. 服务器未启动\n2. 防火墙阻止\n3. IP地址错误\n4. 端口8888被占用`, 'error');
                });
        }
        
        function testWebSocket() {
            const ip = document.getElementById('testIp').value.trim();
            if (!ip) {
                showResult('connectionResult', '请输入服务器IP地址', 'error');
                return;
            }
            
            showResult('connectionResult', '正在测试WebSocket连接...', 'info');
            
            const wsUrl = `ws://${ip}:8888/websocket`;
            const ws = new WebSocket(wsUrl);
            
            const timeout = setTimeout(() => {
                ws.close();
                showResult('connectionResult', 
                    `❌ WebSocket连接超时\n地址：${wsUrl}\n\n请检查：\n1. 服务器是否启动\n2. 防火墙设置\n3. 网络连通性`, 'error');
            }, 10000);
            
            ws.onopen = function() {
                clearTimeout(timeout);
                showResult('connectionResult', 
                    `✅ WebSocket连接成功！\n地址：${wsUrl}`, 'success');
                ws.close();
            };
            
            ws.onerror = function(error) {
                clearTimeout(timeout);
                showResult('connectionResult', 
                    `❌ WebSocket连接失败\n地址：${wsUrl}\n错误：${error}`, 'error');
            };
            
            ws.onclose = function(event) {
                if (event.code !== 1000) {
                    clearTimeout(timeout);
                    showResult('connectionResult', 
                        `❌ WebSocket连接关闭\n代码：${event.code}\n原因：${event.reason}`, 'error');
                }
            };
        }
        
        function runDiagnostics() {
            showResult('diagnosticsResult', '正在运行网络诊断...', 'info');
            
            let diagnostics = '🔍 网络诊断报告\n';
            diagnostics += '==================\n\n';
            
            // 浏览器信息
            diagnostics += `浏览器：${navigator.userAgent}\n`;
            diagnostics += `当前时间：${new Date().toLocaleString()}\n`;
            diagnostics += `页面协议：${window.location.protocol}\n`;
            diagnostics += `页面主机：${window.location.host}\n\n`;
            
            // 网络状态
            if (navigator.onLine) {
                diagnostics += '✅ 网络连接：在线\n';
            } else {
                diagnostics += '❌ 网络连接：离线\n';
            }
            
            // WebSocket支持
            if (window.WebSocket) {
                diagnostics += '✅ WebSocket支持：是\n';
            } else {
                diagnostics += '❌ WebSocket支持：否\n';
            }
            
            diagnostics += '\n📋 建议检查项目：\n';
            diagnostics += '1. 服务器是否启动在8888端口\n';
            diagnostics += '2. 防火墙是否允许8888端口\n';
            diagnostics += '3. 两台电脑是否在同一网络\n';
            diagnostics += '4. 服务器IP地址是否正确\n';
            
            showResult('diagnosticsResult', diagnostics, 'info');
        }
        
        function getNetworkInfo() {
            showResult('diagnosticsResult', '正在获取网络信息...', 'info');
            
            // 尝试获取本地IP（通过WebRTC）
            const pc = new RTCPeerConnection({iceServers: []});
            pc.createDataChannel('');
            
            pc.createOffer().then(offer => pc.setLocalDescription(offer));
            
            pc.onicecandidate = function(event) {
                if (event.candidate) {
                    const candidate = event.candidate.candidate;
                    const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
                    if (ipMatch) {
                        const localIp = ipMatch[1];
                        showResult('diagnosticsResult', 
                            `🌐 检测到的本地IP地址：${localIp}\n\n如果服务器在同一台电脑上，其他电脑应该使用这个IP连接：\nws://${localIp}:8888/websocket`, 'success');
                        pc.close();
                    }
                }
            };
            
            // 备用方案
            setTimeout(() => {
                if (pc.connectionState !== 'closed') {
                    pc.close();
                    showResult('diagnosticsResult', 
                        '⚠️ 无法自动检测IP地址\n\n请手动查找服务器IP：\n1. Windows: 打开命令提示符，输入 ipconfig\n2. 查找"IPv4 地址"', 'warning');
                }
            }, 3000);
        }
        
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }
    </script>
</body>
</html>
