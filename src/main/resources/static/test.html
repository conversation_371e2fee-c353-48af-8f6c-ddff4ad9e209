<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #messages {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        #userList {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>WebSocket 测试页面</h1>

    <div class="container">
        <h3>连接控制</h3>
        <p><strong>WebSocket地址：</strong><span id="wsAddress"></span></p>
        <p><strong>API地址：</strong><span id="apiAddress"></span></p>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h3>消息测试</h3>
        <input type="text" id="messageInput" placeholder="输入消息" style="width: 300px; padding: 5px;">
        <button onclick="sendMessage()" id="sendBtn" disabled>发送消息</button>
        <button onclick="sendPing()" id="pingBtn" disabled>发送心跳</button>
        <button onclick="sendDisconnect()" id="disconnectMsgBtn" disabled>发送断开消息</button>
        <div id="messages"></div>
    </div>

    <div class="container">
        <h3>在线用户信息</h3>
        <button onclick="refreshUserList()">刷新用户列表</button>
        <button onclick="getOnlineCount()">获取在线数量</button>
        <button onclick="triggerHeartbeat()">触发心跳检测</button>
        <button onclick="triggerCleanup()">清理无效连接</button>
        <button onclick="broadcastStatus()">广播服务器状态</button>
        <button onclick="manualDisconnect()">手动断开当前连接</button>
        <div id="userList"></div>
    </div>

    <div class="container">
        <h3>实时通知</h3>
        <div id="notifications"></div>
    </div>

    <script>
        let websocket = null;
        // 自动获取当前页面的主机地址，而不是硬编码localhost
        const currentHost = window.location.hostname;
        const currentPort = window.location.port || '8888';
        const serverUrl = `ws://${currentHost}:${currentPort}/websocket`;
        const apiUrl = `http://${currentHost}:${currentPort}/api/users`;

        // 显示当前使用的连接地址
        console.log('WebSocket地址:', serverUrl);
        console.log('API地址:', apiUrl);

        // 全局变量存储当前会话ID
        let currentSessionId = null;

        function connect() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                addMessage('已经连接了');
                return;
            }

            websocket = new WebSocket(serverUrl);

            websocket.onopen = function(event) {
                addMessage('WebSocket连接成功');
                updateStatus(true);
                updateButtons(true);
            };

            websocket.onmessage = function(event) {
                const data = event.data;
                addMessage('收到消息: ' + data);

                // 尝试解析 JSON 通知
                try {
                    const notification = JSON.parse(data);
                    if (notification.type) {
                        // 保存会话ID
                        if (notification.type === 'connection_established' && notification.sessionId) {
                            currentSessionId = notification.sessionId;
                            addMessage('会话ID已保存: ' + currentSessionId);
                        }
                        addNotification(notification);
                    }
                } catch (e) {
                    // 不是 JSON 格式，忽略
                }
            };

            websocket.onclose = function(event) {
                addMessage('WebSocket连接关闭');
                updateStatus(false);
                updateButtons(false);
            };

            websocket.onerror = function(event) {
                addMessage('WebSocket错误: ' + event);
                updateStatus(false);
                updateButtons(false);
            };
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (message && websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(message);
                addMessage('发送消息: ' + message);
                input.value = '';
            }
        }

        function sendPing() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send('ping');
                addMessage('发送心跳: ping');
            }
        }

        function sendDisconnect() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send('disconnect');
                addMessage('发送断开消息: disconnect');
            }
        }

        function manualDisconnect() {
            if (!currentSessionId) {
                addMessage('错误: 没有有效的会话ID');
                return;
            }

            fetch(apiUrl + '/disconnect/' + currentSessionId, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addMessage('手动断开成功: ' + data.message);
                    } else {
                        addMessage('手动断开失败: ' + data.message);
                    }
                })
                .catch(error => {
                    addMessage('手动断开请求失败: ' + error);
                });
        }

        function addMessage(message) {
            const messages = document.getElementById('messages');
            const time = new Date().toLocaleTimeString();
            messages.innerHTML += `<div>[${time}] ${message}</div>`;
            messages.scrollTop = messages.scrollHeight;
        }

        function updateStatus(connected) {
            const status = document.getElementById('status');
            if (connected) {
                status.textContent = '已连接';
                status.className = 'status connected';
            } else {
                status.textContent = '未连接';
                status.className = 'status disconnected';
            }
        }

        function updateButtons(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('sendBtn').disabled = !connected;
            document.getElementById('pingBtn').disabled = !connected;
            document.getElementById('disconnectMsgBtn').disabled = !connected;
        }

        function refreshUserList() {
            fetch(apiUrl + '/online-users')
                .then(response => response.json())
                .then(data => {
                    const userList = document.getElementById('userList');
                    if (data.success) {
                        let html = `<strong>在线用户数: ${data.count}</strong><br><br>`;
                        data.users.forEach(user => {
                            html += `<div>IP: ${user.ipAddress} | 连接时间: ${user.connectTime}</div>`;
                        });
                        userList.innerHTML = html;
                    } else {
                        userList.innerHTML = '获取用户列表失败';
                    }
                })
                .catch(error => {
                    document.getElementById('userList').innerHTML = '请求失败: ' + error;
                });
        }

        function getOnlineCount() {
            fetch(apiUrl + '/online-count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addMessage(`当前在线用户数: ${data.count}`);
                    }
                })
                .catch(error => {
                    addMessage('获取在线数量失败: ' + error);
                });
        }

        function triggerHeartbeat() {
            fetch(apiUrl + '/trigger-heartbeat', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addMessage('心跳检测已触发');
                    }
                })
                .catch(error => {
                    addMessage('触发心跳检测失败: ' + error);
                });
        }

        function triggerCleanup() {
            fetch(apiUrl + '/trigger-cleanup', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addMessage('清理无效连接已触发');
                    }
                })
                .catch(error => {
                    addMessage('触发清理失败: ' + error);
                });
        }

        function broadcastStatus() {
            fetch(apiUrl + '/broadcast-status', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addMessage('服务器状态已广播');
                    }
                })
                .catch(error => {
                    addMessage('广播状态失败: ' + error);
                });
        }

        function addNotification(notification) {
            const notifications = document.getElementById('notifications');
            const time = new Date().toLocaleTimeString();
            let message = '';

            switch (notification.type) {
                case 'connection_established':
                    message = `[${time}] 连接建立: SessionId=${notification.sessionId}, IP=${notification.ip}`;
                    break;
                case 'disconnect_confirmed':
                    message = `[${time}] 断开确认: ${notification.message}`;
                    break;
                case 'server_disconnect':
                    message = `[${time}] 服务器断开: ${notification.message}`;
                    break;
                case 'user_connected':
                    message = `[${time}] 用户连接: IP=${notification.ip}, 在线数=${notification.onlineCount}`;
                    break;
                case 'user_disconnected':
                    message = `[${time}] 用户断开: IP=${notification.ip}, 在线数=${notification.onlineCount}`;
                    break;
                case 'heartbeat_timeout':
                    message = `[${time}] 心跳超时: IP=${notification.ip}, 在线数=${notification.onlineCount}`;
                    break;
                case 'server_status':
                    message = `[${time}] 服务器状态: 在线=${notification.onlineCount}, 唯一IP=${notification.uniqueIpCount}`;
                    break;
                case 'echo':
                    message = `[${time}] 回显消息: ${notification.originalMessage}`;
                    break;
                case 'json_response':
                    message = `[${time}] JSON响应: 已接收`;
                    break;
                case 'error':
                    message = `[${time}] 错误: ${notification.message}`;
                    break;
                default:
                    message = `[${time}] 未知通知: ${JSON.stringify(notification)}`;
            }

            notifications.innerHTML += `<div style="color: #007bff;">${message}</div>`;
            notifications.scrollTop = notifications.scrollHeight;
        }

        // 页面加载时自动刷新用户列表
        window.onload = function() {
            // 显示连接地址
            document.getElementById('wsAddress').textContent = serverUrl;
            document.getElementById('apiAddress').textContent = apiUrl;

            refreshUserList();
            // 每5秒自动刷新一次用户列表
            setInterval(refreshUserList, 5000);
        };

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
