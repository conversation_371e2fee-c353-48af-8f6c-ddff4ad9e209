<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #messages {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        #userList {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>WebSocket 测试页面</h1>
    
    <div class="container">
        <h3>连接控制</h3>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
        <div id="status" class="status disconnected">未连接</div>
    </div>
    
    <div class="container">
        <h3>消息测试</h3>
        <input type="text" id="messageInput" placeholder="输入消息" style="width: 300px; padding: 5px;">
        <button onclick="sendMessage()" id="sendBtn" disabled>发送消息</button>
        <button onclick="sendPing()" id="pingBtn" disabled>发送心跳</button>
        <div id="messages"></div>
    </div>
    
    <div class="container">
        <h3>在线用户信息</h3>
        <button onclick="refreshUserList()">刷新用户列表</button>
        <button onclick="getOnlineCount()">获取在线数量</button>
        <div id="userList"></div>
    </div>

    <script>
        let websocket = null;
        const serverUrl = 'ws://localhost:8888/websocket';
        const apiUrl = 'http://localhost:8888/api/users';
        
        function connect() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                addMessage('已经连接了');
                return;
            }
            
            websocket = new WebSocket(serverUrl);
            
            websocket.onopen = function(event) {
                addMessage('WebSocket连接成功');
                updateStatus(true);
                updateButtons(true);
            };
            
            websocket.onmessage = function(event) {
                addMessage('收到消息: ' + event.data);
            };
            
            websocket.onclose = function(event) {
                addMessage('WebSocket连接关闭');
                updateStatus(false);
                updateButtons(false);
            };
            
            websocket.onerror = function(event) {
                addMessage('WebSocket错误: ' + event);
                updateStatus(false);
                updateButtons(false);
            };
        }
        
        function disconnect() {
            if (websocket) {
                websocket.close();
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (message && websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(message);
                addMessage('发送消息: ' + message);
                input.value = '';
            }
        }
        
        function sendPing() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send('ping');
                addMessage('发送心跳: ping');
            }
        }
        
        function addMessage(message) {
            const messages = document.getElementById('messages');
            const time = new Date().toLocaleTimeString();
            messages.innerHTML += `<div>[${time}] ${message}</div>`;
            messages.scrollTop = messages.scrollHeight;
        }
        
        function updateStatus(connected) {
            const status = document.getElementById('status');
            if (connected) {
                status.textContent = '已连接';
                status.className = 'status connected';
            } else {
                status.textContent = '未连接';
                status.className = 'status disconnected';
            }
        }
        
        function updateButtons(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('sendBtn').disabled = !connected;
            document.getElementById('pingBtn').disabled = !connected;
        }
        
        function refreshUserList() {
            fetch(apiUrl + '/online-users')
                .then(response => response.json())
                .then(data => {
                    const userList = document.getElementById('userList');
                    if (data.success) {
                        let html = `<strong>在线用户数: ${data.count}</strong><br><br>`;
                        data.users.forEach(user => {
                            html += `<div>IP: ${user.ipAddress} | 连接时间: ${user.connectTime}</div>`;
                        });
                        userList.innerHTML = html;
                    } else {
                        userList.innerHTML = '获取用户列表失败';
                    }
                })
                .catch(error => {
                    document.getElementById('userList').innerHTML = '请求失败: ' + error;
                });
        }
        
        function getOnlineCount() {
            fetch(apiUrl + '/online-count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addMessage(`当前在线用户数: ${data.count}`);
                    }
                })
                .catch(error => {
                    addMessage('获取在线数量失败: ' + error);
                });
        }
        
        // 页面加载时自动刷新用户列表
        window.onload = function() {
            refreshUserList();
            // 每5秒自动刷新一次用户列表
            setInterval(refreshUserList, 5000);
        };
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
